# Dependencies
node_modules/
*/node_modules/
frontend/node_modules/
backend/node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
*/package-lock.json
*/yarn.lock

# Environment variables
.env
*/.env
backend/.env
frontend/.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*/.env.local
*/.env.development.local
*/.env.test.local
*/.env.production.local

# Build files
/build
/dist
*/build/
*/dist/
frontend/build/
frontend/dist/
backend/dist/
/.next
/out
frontend/.next/
frontend/out/

# Debug logs
logs
*/logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editors
.idea/
.vscode/
*/.idea/
*/.vscode/
*.swp
*.swo
.DS_Store
*/.DS_Store
.project
.classpath
.settings/
*.sublime-workspace
*.sublime-project

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
/coverage
*/coverage/
frontend/coverage/
backend/coverage/
/.nyc_output
*/.nyc_output/

# frontend-specific
frontend/.cache/
frontend/.temp/
frontend/.eslintcache
frontend/public/env.js

# backend-specific
backend/uploads/
backend/tmp/

# Misc
.cache/
.temp/
*/.cache/
*/.temp/
.eslintcache
*/.eslintcache
