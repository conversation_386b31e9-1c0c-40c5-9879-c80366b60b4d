const bcrypt = require('bcryptjs');
const { User, Appointment, Barber, Service } = require('../models');
const { generateToken } = require('../utils/jwt');
const { Op } = require('sequelize');

/**
 * Register a new user
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const registerUser = async (req, res) => {
  try {
    const { first_name, last_name, email, phone, password } = req.body;

    // Validate required fields
    if (!first_name || !last_name || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'First name, last name, email, and password are required.'
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid email address.'
      });
    }

    // Validate password strength
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 6 characters long.'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [
          { email: email.toLowerCase() },
          ...(phone ? [{ phone }] : [])
        ]
      }
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'User with this email or phone already exists.'
      });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create new user
    const newUser = await User.create({
      firstName: first_name,
      lastName: last_name,
      email: email.toLowerCase(),
      phone: phone || null,
      passwordHash
    });

    // Generate JWT token
    const token = generateToken(newUser.uId, 'user');

    // Return success response (exclude password hash)
    const userResponse = {
      uId: newUser.uId,
      firstName: newUser.firstName,
      lastName: newUser.lastName,
      email: newUser.email,
      phone: newUser.phone,
      profileImageUrl: newUser.profileImageUrl,
      createdAt: newUser.createdAt
    };

    res.status(201).json({
      success: true,
      message: 'User registered successfully.',
      data: {
        user: userResponse,
        token
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during registration.'
    });
  }
};

/**
 * Login user with email/phone and password
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const loginUser = async (req, res) => {
  try {
    const { email, phone, password } = req.body;

    // Validate required fields
    if ((!email && !phone) || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email or phone, and password are required.'
      });
    }

    // Find user by email or phone
    const whereCondition = {};
    if (email) {
      whereCondition.email = email.toLowerCase();
    } else if (phone) {
      whereCondition.phone = phone;
    }

    const user = await User.findOne({
      where: whereCondition
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials.'
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials.'
      });
    }

    // Generate JWT token
    const token = generateToken(user.uId, 'user');

    // Return success response (exclude password hash)
    const userResponse = {
      uId: user.uId,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      profileImageUrl: user.profileImageUrl
    };

    res.status(200).json({
      success: true,
      message: 'Login successful.',
      data: {
        user: userResponse,
        token
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during login.'
    });
  }
};

/**
 * Get user profile by ID (protected route)
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getUserProfile = async (req, res) => {
  try {
    const userId = req.user.userId;

    const user = await User.findByPk(userId, {
      attributes: ['uId', 'firstName', 'lastName', 'email', 'phone', 'profileImageUrl', 'createdAt', 'updatedAt']
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found.'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        user
      }
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching profile.'
    });
  }
};

/**
 * Get user booking history (past & upcoming appointments)
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getUserBookingHistory = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { status, limit = 20, offset = 0 } = req.query;

    // Build where condition
    const whereCondition = { userId };
    if (status) {
      whereCondition.status = status;
    }

    const appointments = await Appointment.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: Barber,
          as: 'barber',
          attributes: ['bId', 'firstName', 'lastName', 'shopName', 'profileImageUrl']
        },
        {
          model: Service,
          as: 'service',
          attributes: ['id', 'name', 'description', 'durationMinutes', 'price']
        }
      ],
      order: [['appointmentTime', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Separate past and upcoming appointments
    const now = new Date();
    const pastAppointments = [];
    const upcomingAppointments = [];

    appointments.rows.forEach(appointment => {
      if (new Date(appointment.appointmentTime) < now) {
        pastAppointments.push(appointment);
      } else {
        upcomingAppointments.push(appointment);
      }
    });

    res.status(200).json({
      success: true,
      data: {
        total: appointments.count,
        pastAppointments,
        upcomingAppointments,
        pagination: {
          limit: parseInt(limit),
          offset: parseInt(offset),
          total: appointments.count
        }
      }
    });

  } catch (error) {
    console.error('Get booking history error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching booking history.'
    });
  }
};

/**
 * Update user profile
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const updateUserProfile = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { first_name, last_name, phone, profile_image_url } = req.body;

    // Find user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found.'
      });
    }

    // Prepare update data
    const updateData = {};
    if (first_name !== undefined) updateData.firstName = first_name;
    if (last_name !== undefined) updateData.lastName = last_name;
    if (phone !== undefined) updateData.phone = phone;
    if (profile_image_url !== undefined) updateData.profileImageUrl = profile_image_url;

    // Check if phone is being updated and already exists
    if (phone && phone !== user.phone) {
      const existingUser = await User.findOne({
        where: {
          phone,
          uId: { [Op.ne]: userId }
        }
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: 'Phone number already exists.'
        });
      }
    }

    // Update user
    await user.update(updateData);

    // Return updated user (exclude password hash)
    const updatedUser = {
      uId: user.uId,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      profileImageUrl: user.profileImageUrl,
      updatedAt: user.updatedAt
    };

    res.status(200).json({
      success: true,
      message: 'Profile updated successfully.',
      data: {
        user: updatedUser
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while updating profile.'
    });
  }
};

module.exports = {
  registerUser,
  loginUser,
  getUserProfile,
  getUserBookingHistory,
  updateUserProfile
};
