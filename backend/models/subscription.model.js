const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Subscription = sequelize.define('Subscription', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    barberId: {
      type: DataTypes.UUID,
      allowNull: false,
      unique: true,
      field: 'barber_id',
      references: {
        model: 'barbers',
        key: 'b_id'
      }
    },
    planName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'plan_name'
    },
    pricePerMonth: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      field: 'price_per_month',
      validate: {
        min: 0
      }
    },
    startedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'started_at'
    },
    endsAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'ends_at'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_active'
    }
  }, {
    tableName: 'subscriptions',
    timestamps: false, // Using custom started_at and ends_at fields
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['barber_id']
      },
      {
        fields: ['is_active']
      },
      {
        fields: ['started_at']
      },
      {
        fields: ['ends_at']
      }
    ],
    validate: {
      endsAtAfterStartedAt() {
        if (this.startedAt >= this.endsAt) {
          throw new Error('End date must be after start date');
        }
      }
    }
  });

  Subscription.associate = (models) => {
    // Subscription belongs to Barber
    Subscription.belongsTo(models.Barber, {
      foreignKey: 'barberId',
      as: 'barber',
      onDelete: 'CASCADE'
    });
  };

  return Subscription;
};
