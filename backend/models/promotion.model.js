const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Promotion = sequelize.define('Promotion', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    barberId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'barber_id',
      references: {
        model: 'barbers',
        key: 'b_id'
      }
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    discountPercent: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'discount_percent',
      validate: {
        min: 1,
        max: 100
      }
    },
    validFrom: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'valid_from'
    },
    validUntil: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'valid_until'
    }
  }, {
    tableName: 'promotions',
    timestamps: true,
    underscored: true,
    updatedAt: false, // Only track created_at for promotions
    indexes: [
      {
        fields: ['barber_id']
      },
      {
        fields: ['valid_from']
      },
      {
        fields: ['valid_until']
      },
      {
        fields: ['valid_from', 'valid_until']
      }
    ],
    validate: {
      validUntilAfterValidFrom() {
        if (this.validFrom >= this.validUntil) {
          throw new Error('Valid until date must be after valid from date');
        }
      }
    }
  });

  Promotion.associate = (models) => {
    // Promotion belongs to Barber
    Promotion.belongsTo(models.Barber, {
      foreignKey: 'barberId',
      as: 'barber',
      onDelete: 'CASCADE'
    });
  };

  return Promotion;
};
