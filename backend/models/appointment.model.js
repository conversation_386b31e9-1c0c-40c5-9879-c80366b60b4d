const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Appointment = sequelize.define('Appointment', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'u_id'
      }
    },
    barberId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'barber_id',
      references: {
        model: 'barbers',
        key: 'b_id'
      }
    },
    serviceId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'service_id',
      references: {
        model: 'services',
        key: 'id'
      }
    },
    appointmentTime: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'appointment_time'
    },
    status: {
      type: DataTypes.ENUM('booked', 'cancelled', 'completed', 'rescheduled'),
      allowNull: false,
      defaultValue: 'booked'
    },
    rescheduledFrom: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'rescheduled_from',
      references: {
        model: 'appointments',
        key: 'id'
      }
    }
  }, {
    tableName: 'appointments',
    timestamps: true,
    underscored: true,
    updatedAt: false, // Only track created_at for appointments
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['barber_id']
      },
      {
        fields: ['service_id']
      },
      {
        fields: ['appointment_time']
      },
      {
        fields: ['status']
      },
      {
        unique: true,
        fields: ['barber_id', 'appointment_time'],
        name: 'unique_barber_appointment_time'
      }
    ]
  });

  Appointment.associate = (models) => {
    // Appointment belongs to User
    Appointment.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE'
    });

    // Appointment belongs to Barber
    Appointment.belongsTo(models.Barber, {
      foreignKey: 'barberId',
      as: 'barber',
      onDelete: 'CASCADE'
    });

    // Appointment belongs to Service
    Appointment.belongsTo(models.Service, {
      foreignKey: 'serviceId',
      as: 'service',
      onDelete: 'RESTRICT'
    });

    // Appointment has one Reminder
    Appointment.hasOne(models.Reminder, {
      foreignKey: 'appointmentId',
      as: 'reminder',
      onDelete: 'CASCADE'
    });

    // Self-referencing for rescheduled appointments
    Appointment.belongsTo(models.Appointment, {
      foreignKey: 'rescheduledFrom',
      as: 'originalAppointment',
      onDelete: 'SET NULL'
    });

    Appointment.hasMany(models.Appointment, {
      foreignKey: 'rescheduledFrom',
      as: 'rescheduledAppointments',
      onDelete: 'SET NULL'
    });
  };

  return Appointment;
};
