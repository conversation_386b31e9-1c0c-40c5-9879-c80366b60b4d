const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Service = sequelize.define('Service', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    barberId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'barber_id',
      references: {
        model: 'barbers',
        key: 'b_id'
      }
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    durationMinutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'duration_minutes',
      validate: {
        min: 1
      }
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    }
  }, {
    tableName: 'services',
    timestamps: true,
    underscored: true,
    updatedAt: false, // Only track created_at for services
    indexes: [
      {
        fields: ['barber_id']
      },
      {
        fields: ['name']
      }
    ]
  });

  Service.associate = (models) => {
    // Service belongs to Barber
    Service.belongsTo(models.Barber, {
      foreignKey: 'barberId',
      as: 'barber',
      onDelete: 'CASCADE'
    });

    // Service is referenced by Appointments
    Service.hasMany(models.Appointment, {
      foreignKey: 'serviceId',
      as: 'appointments',
      onDelete: 'RESTRICT' // Prevent deletion if appointments exist
    });
  };

  return Service;
};
