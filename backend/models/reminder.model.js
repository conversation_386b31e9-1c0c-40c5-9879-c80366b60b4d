const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Reminder = sequelize.define('Reminder', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    appointmentId: {
      type: DataTypes.UUID,
      allowNull: false,
      unique: true,
      field: 'appointment_id',
      references: {
        model: 'appointments',
        key: 'id'
      }
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'u_id'
      }
    },
    barberId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'barber_id',
      references: {
        model: 'barbers',
        key: 'b_id'
      }
    },
    reminderTime: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'reminder_time'
    },
    method: {
      type: DataTypes.ENUM('sms', 'email', 'in-app'),
      allowNull: false,
      defaultValue: 'email'
    },
    sent: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    }
  }, {
    tableName: 'reminders',
    timestamps: false, // No timestamps needed for reminders
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['appointment_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['barber_id']
      },
      {
        fields: ['reminder_time']
      },
      {
        fields: ['sent']
      },
      {
        fields: ['method']
      }
    ]
  });

  Reminder.associate = (models) => {
    // Reminder belongs to Appointment
    Reminder.belongsTo(models.Appointment, {
      foreignKey: 'appointmentId',
      as: 'appointment',
      onDelete: 'CASCADE'
    });

    // Reminder belongs to User
    Reminder.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE'
    });

    // Reminder belongs to Barber
    Reminder.belongsTo(models.Barber, {
      foreignKey: 'barberId',
      as: 'barber',
      onDelete: 'CASCADE'
    });
  };

  return Reminder;
};
