const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AvailabilitySlot = sequelize.define('AvailabilitySlot', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    barberId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'barber_id',
      references: {
        model: 'barbers',
        key: 'b_id'
      }
    },
    dayOfWeek: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'day_of_week',
      validate: {
        min: 0,
        max: 6
      },
      comment: '0 = Sunday, 1 = Monday, ..., 6 = Saturday'
    },
    startTime: {
      type: DataTypes.TIME,
      allowNull: false,
      field: 'start_time'
    },
    endTime: {
      type: DataTypes.TIME,
      allowNull: false,
      field: 'end_time'
    },
    isBreak: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'is_break',
      comment: 'True if this slot represents a break time'
    }
  }, {
    tableName: 'availability_slots',
    timestamps: false, // No timestamps needed for availability slots
    underscored: true,
    indexes: [
      {
        fields: ['barber_id']
      },
      {
        fields: ['day_of_week']
      },
      {
        fields: ['barber_id', 'day_of_week']
      }
    ],
    validate: {
      endTimeAfterStartTime() {
        if (this.startTime >= this.endTime) {
          throw new Error('End time must be after start time');
        }
      }
    }
  });

  AvailabilitySlot.associate = (models) => {
    // AvailabilitySlot belongs to Barber
    AvailabilitySlot.belongsTo(models.Barber, {
      foreignKey: 'barberId',
      as: 'barber',
      onDelete: 'CASCADE'
    });
  };

  return AvailabilitySlot;
};
