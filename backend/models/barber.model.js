const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Barber = sequelize.define('Barber', {
    bId: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      field: 'b_id'
    },
    firstName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'first_name'
    },
    lastName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'last_name'
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    passwordHash: {
      type: DataTypes.STRING(255),
      allowNull: false,
      field: 'password_hash'
    },
    profileImageUrl: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'profile_image_url'
    },
    bio: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    shopName: {
      type: DataTypes.STRING(255),
      allowNull: true,
      field: 'shop_name'
    },
    locationLat: {
      type: DataTypes.DECIMAL(10, 8),
      allowNull: true,
      field: 'location_lat'
    },
    locationLng: {
      type: DataTypes.DECIMAL(11, 8),
      allowNull: true,
      field: 'location_lng'
    },
    isFeatured: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_featured'
    }
  }, {
    tableName: 'barbers',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['email']
      },
      {
        fields: ['is_featured']
      }
    ]
  });

  Barber.associate = (models) => {
    // Barber has many Appointments
    Barber.hasMany(models.Appointment, {
      foreignKey: 'barberId',
      as: 'appointments',
      onDelete: 'CASCADE'
    });

    // Barber has many Services
    Barber.hasMany(models.Service, {
      foreignKey: 'barberId',
      as: 'services',
      onDelete: 'CASCADE'
    });

    // Barber has many AvailabilitySlots
    Barber.hasMany(models.AvailabilitySlot, {
      foreignKey: 'barberId',
      as: 'availabilitySlots',
      onDelete: 'CASCADE'
    });

    // Barber has many Promotions
    Barber.hasMany(models.Promotion, {
      foreignKey: 'barberId',
      as: 'promotions',
      onDelete: 'CASCADE'
    });

    // Barber has one Subscription
    Barber.hasOne(models.Subscription, {
      foreignKey: 'barberId',
      as: 'subscription',
      onDelete: 'CASCADE'
    });

    // Barber has many Reminders
    Barber.hasMany(models.Reminder, {
      foreignKey: 'barberId',
      as: 'reminders',
      onDelete: 'CASCADE'
    });
  };

  return Barber;
};
