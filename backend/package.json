{"name": "barber-booking-backend", "version": "1.0.0", "description": "Backend API for Barber Booking System", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "seed:undo": "npx sequelize-cli db:seed:undo:all", "db:create": "npx sequelize-cli db:create", "db:drop": "npx sequelize-cli db:drop"}, "keywords": ["barber", "booking", "appointment", "api", "nodejs", "sequelize", "postgresql"], "author": "Your Name", "license": "MIT", "dependencies": {"sequelize": "^6.35.0", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "sequelize-cli": "^6.6.2"}, "engines": {"node": ">=16.0.0"}}