const express = require('express');
const router = express.Router();
const {
  registerUser,
  loginUser,
  getUserProfile,
  getUserBookingHistory,
  updateUserProfile
} = require('../controllers/user.controller');
const { verifyToken } = require('../middleware/auth.middleware');

// Public routes (no authentication required)
/**
 * @route   POST /api/users/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', registerUser);

/**
 * @route   POST /api/users/login
 * @desc    Login user with email/phone and password
 * @access  Public
 */
router.post('/login', loginUser);

// Protected routes (authentication required)
/**
 * @route   GET /api/users/profile
 * @desc    Get user profile by ID
 * @access  Private (User)
 */
router.get('/profile', verifyToken, getUserProfile);

/**
 * @route   GET /api/users/bookings
 * @desc    Get user booking history (past & upcoming appointments)
 * @access  Private (User)
 * @query   status - Filter by appointment status (optional)
 * @query   limit - Number of results per page (default: 20)
 * @query   offset - Number of results to skip (default: 0)
 */
router.get('/bookings', verifyToken, getUserBookingHistory);

/**
 * @route   PUT /api/users/profile
 * @desc    Update user profile
 * @access  Private (User)
 */
router.put('/profile', verifyToken, updateUserProfile);

module.exports = router;
