# Barber Booking System - Backend Models

This backend uses Sequelize ORM with PostgreSQL to manage a comprehensive barber booking system.

## 📁 Project Structure

```
backend/
├── models/
│   ├── index.js              # Model initialization and associations
│   ├── user.model.js         # User model
│   ├── barber.model.js       # Barber model
│   ├── service.model.js      # Service model
│   ├── appointment.model.js  # Appointment model
│   ├── availability.model.js # Availability slot model
│   ├── promotion.model.js    # Promotion model
│   ├── subscription.model.js # Subscription model
│   └── reminder.model.js     # Reminder model
├── config/
│   └── config.js            # Sequelize configuration
├── package.json             # Dependencies and scripts
├── .env.example            # Environment variables template
└── README.md               # This file
```

## 🗄️ Database Models

### 1. User Model (`user.model.js`)
- **Primary Key**: `u_id` (UUID)
- **Fields**: firstName, lastName, email (unique), phone, passwordHash, profileImageUrl
- **Associations**: 
  - Has many Appointments
  - Has many Reminders

### 2. Barber Model (`barber.model.js`)
- **Primary Key**: `b_id` (UUID)
- **Fields**: firstName, lastName, email (unique), phone, passwordHash, profileImageUrl, bio, shopName, locationLat, locationLng, isFeatured
- **Associations**:
  - Has many Appointments
  - Has many Services
  - Has many AvailabilitySlots
  - Has many Promotions
  - Has one Subscription

### 3. Service Model (`service.model.js`)
- **Primary Key**: `id` (UUID)
- **Fields**: barberId (FK), name, description, durationMinutes, price
- **Associations**:
  - Belongs to Barber
  - Referenced by Appointments

### 4. Appointment Model (`appointment.model.js`)
- **Primary Key**: `id` (UUID)
- **Fields**: userId (FK), barberId (FK), serviceId (FK), appointmentTime, status, rescheduledFrom
- **Status Values**: 'booked', 'cancelled', 'completed', 'rescheduled'
- **Associations**:
  - Belongs to User, Barber, and Service
  - Has one Reminder
  - Self-referencing for rescheduled appointments

### 5. AvailabilitySlot Model (`availability.model.js`)
- **Primary Key**: `id` (UUID)
- **Fields**: barberId (FK), dayOfWeek (0-6), startTime, endTime, isBreak
- **Associations**:
  - Belongs to Barber

### 6. Promotion Model (`promotion.model.js`)
- **Primary Key**: `id` (UUID)
- **Fields**: barberId (FK), title, discountPercent, validFrom, validUntil
- **Associations**:
  - Belongs to Barber

### 7. Subscription Model (`subscription.model.js`)
- **Primary Key**: `id` (UUID)
- **Fields**: barberId (FK), planName, pricePerMonth, startedAt, endsAt, isActive
- **Associations**:
  - Belongs to Barber

### 8. Reminder Model (`reminder.model.js`)
- **Primary Key**: `id` (UUID)
- **Fields**: appointmentId (FK), userId (FK), barberId (FK) reminderTime, method, sent
- **Method Values**: 'sms', 'email', 'in-app'
- **Associations**:
  - Belongs to Appointment, User and Barber

## 🔗 Key Relationships

```
User (1) ──── (M) Appointment (M) ──── (1) Barber
                     │
                     └── (1) Service
                     │
                     └── (1) Reminder

Barber (1) ──── (M) Service
         │
         ├── (M) AvailabilitySlot
         │
         ├── (M) Promotion
         │
         |── (1) Subscription
         │
         └── (1) Reminder
```

## 🚀 Getting Started

1. **Install Dependencies**:
   ```bash
   cd backend
   npm install
   ```

2. **Set up Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

3. **Create Database**:
   ```bash
   npm run db:create
   ```

4. **Run Migrations** (if you create them):
   ```bash
   npm run migrate
   ```

5. **Start Development Server**:
   ```bash
   npm run dev
   ```

## 🔧 Key Features

- **UUID Primary Keys**: All models use UUIDs for better scalability
- **Proper Associations**: Full relationship mapping with foreign key constraints
- **Data Validation**: Built-in Sequelize validations for data integrity
- **Indexes**: Strategic indexing for query performance
- **Timestamps**: Automatic created_at/updated_at tracking where appropriate
- **Snake Case DB Columns**: Database uses snake_case while JS uses camelCase
- **Cascade Deletes**: Proper cleanup when parent records are deleted

## 📝 Notes

- All models follow Sequelize best practices
- Foreign key constraints are properly defined
- Unique constraints are applied where necessary
- Validation rules ensure data integrity
- Models are designed for scalability and performance
